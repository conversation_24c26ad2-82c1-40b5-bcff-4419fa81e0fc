package com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.SensitiveWordsService;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import com.teyuntong.outer.export.service.service.common.property.GroupOcrProperties;
import com.teyuntong.outer.export.service.service.remote.common.TytConfigRemoteService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;

import static com.wlqq.wallet.gateway.client.util.sign.RSA.sign;

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(GroupOcrProperties.class)
public class SensitiveWordsServiceImpl implements SensitiveWordsService {

    private final MBOpenPlatformUtil util;

    private final TytConfigRemoteService tytConfigRemoteService;

    @Override
    public SensitiveWordsVO newCargoSensitiveCheck(Long userId, String taskContent, String machineRemark) {
        log.info("调用集团敏感词接口 请求参数：{} {} {}", userId, taskContent, machineRemark);

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            String content = "{\"interfaceName\":\"CargoSensitiveCheck\",\"params\":{\"description\":\"草泥马\",\"type\":2}}";
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, content);
            Request request = new Request.Builder()
                    .url(
                            "https://dev.ymm56.com/risk-access-service/RiskAccessFacade/riskExternalCommonRuleIface")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-app-id", "risk-access-service")
                    .addHeader("x-sign-type", "RSA")
                    .addHeader("x-sign", sign(content,
                            "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCbVftVEN0GpI8SNbgeVZ0hu84LL1nTZQ3aWPZdGgtjldl+em/6gNzLsm3KRkukBCvLgIdbo9JP/Th2fUpKmiRSik5lMPS+vluzSMpYE0RrAyeKM1H68D6BtLH5sVu9P3Itxa2WTmWsk70GRVwpW2ReMlYcb+Z52YozEA4AXSlgWERScpL8eFzGqnikZ9rrHoqjd9cUo8+jiCd7Cyh7VHMT1KxcRW4wkj0Z8KxiyGW7MzGmBwlZ61fqub+JjtNYNtiUw09dRVIKB/BaeTOIvy0w/Nf2bnfuesV/apHvIryEf6IdSDqTa08lRVccVZ0xAeyg21DFvuiSpDzwycY/9oq3AgMBAAECggEBAJQRA3/0kRrx5UbQWe7gYOV05VSYECGHJAtL3ZqlZtHIVwdOokrAZrbsH+c91oBDUZOrslrR73k7wISBrT0rbRAYsd2XKtRfE330FpyZ0Q/Ic/oyLSTruEle+n8uZmogfi/PTEYH+LbmJLpDhzQM38FCbf6xU5o2mJtP5GjIZNZmECm/qUHrIkYFrBgg308gJVs1mNXyYJhghQm5xpildX5Bj9cqw0G3JEqTe21q5Plb3TdzLmlTYvCAEjwGvj2ZMV3vPka0BDgXeSMGJTNpWjBO1gTw8GIfqKp7zFITPGgHhIRL/GY+XTFPLeqnik2lkfq8RigusYYjKV9+ji37IPECgYEA0W64eMKoRkPYj8Sn2xDKNBMI1EjYxja0Axygul+uShhjz2vLZFIBwGMB3TgRaSJ4gtcw0evXDHs0Unyz8XlVSuLQsvNovuAuqKioyYt3RJsOQaJMAHKqEBT06+LXPDgruL+/D2qNUpa+nQeUhkDahNNRvRoMjrXvoGPSsoyPUYsCgYEAvd/7bZi6dUSF2+73RFRzexA2aNylrXJ+5gePYl6kdGsyQQAi/OB7CdM7/YcOm5rZOgsVXeQ1RiZTzgYr22cTQgR2PKC7cW/PfTQsY0uMv3GHlmtyuV3NNKxtPpddh746G8ma96F//Vx9E0L/eR7a2UptDz0pr13tkSwIOWRGOQUCgYA8W/S+mRt2ZMddaAgYktYz++hnhjczqUBoRS/thvXsVOsvNJoB58Cped9nqmkaK90fmHVxis8Hwdpqi5Yw2g9C7evIJ27g1CueYD2FGLTfCPEq6WOOHX319JRCB7aQnH+9V0XxY3wneVqmVzDvIHHaJi1AmulTmAvf5eUroGcN0QKBgQClJ+c+DEPbbs/0XuaLc93C5HY/PfWKafYBRgAtM19euENNEL5eMTA0ep1KyCgnp0hd2XgrZC4Gp53rraiGPiEdIl5qG/EPGY9kO3aEpBl4CFtEto9itzmgIH8hm23KfedzdigqTzrR+Zn9oCAxjWoU6Uz1nWDd3hifVqqMqTD/tQKBgQCpTe794fJwu2CXrNT7ougavlXnG2ex57ybCBvobP8/JrmZY/tS3HNpy7Q/7QCoqDlGGlC6ZhjHYld6oDt2reYgffR1CzBtobsKjaBvkYlZqst++hfRlpr2aDEXOjijRywlCZBLDlonVqWnQ7LEDTwx5FITsal5wQBZ1GD8KJXiTQ==",
                            "UTF-8"))
                    .build();
            Response response = client.newCall(request).execute();
            log.info("调用集团敏感词接口（试用） 返回信息：code:{} msg:{}",  response.code(), response.message());
            if (response.isSuccessful() && response.body() != null) {
                log.info("调用集团敏感词接口（试用） 返回信息：data:{}",  JSON.toJSONString(response.body()));
            }
        } catch (Exception e) {
            log.info("调用集团敏感词接口（试用） 异常：",  e);
        }


        if (StringUtil.isBlank(taskContent) && StringUtil.isBlank(machineRemark)) {
            return SensitiveWordsVO.builder()
                    .taskContentHitSensitiveWords(false)
                    .machineRemarkHitSensitiveWords(false)
                    .sensitiveWords(new HashSet<>())
                    .build();
        }

        HashMap<String, String> param = new HashMap<>();
        param.put("cargoUserId", String.valueOf(userId));
        param.put("filterType", "0");
        param.put("securedTran", "2");
        param.put("cargoName", StringUtil.isNotBlank(taskContent) ? taskContent : "");
        param.put("description", StringUtil.isNotBlank(machineRemark) ? machineRemark : "");
        param.put("ticketType", "1");
        param.put("scene", "tyt");

        // JSON 请求体
        MediaType jsonMediaType = MediaType.parse("application/json; charset=utf-8");
        HashMap<String, Object> payload = new HashMap<>();
        payload.put("interfaceName", "NewCargoSensitiveCheck");
        payload.put("param", param);
        RequestBody body = RequestBody.create(JSON.toJSONString(payload), jsonMediaType);

//        String responseBodyStr = util.doPost(Constants.NEW_CARGO_SENSITIVE_CHECK_URL, body);

        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";

        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";

        String sensitiveResponse2 = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";

        String sensitiveResponse3 = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";


        String responseBodyStr;
        Integer cargoSensitiveCheck = tytConfigRemoteService.getIntValue("cargo_sensitive_check");
        if (cargoSensitiveCheck == null || cargoSensitiveCheck == 0) {
            responseBodyStr = acceptResponse;
        } else if (cargoSensitiveCheck == 1) {
            responseBodyStr = sensitiveResponse;
        } else if (cargoSensitiveCheck == 2) {
            responseBodyStr = sensitiveResponse2;
        } else {
            responseBodyStr = sensitiveResponse3;
        }

        log.info("调用集团敏感词接口 返回信息：{}", responseBodyStr);

        SensitiveWordsVO sensitiveWordsVO = parseSensitiveCheckResponse(responseBodyStr);

        log.info("调用集团敏感词接口 解析处理后得到的信息：{}", JSON.toJSONString(sensitiveWordsVO));

        return sensitiveWordsVO;
    }

    /**
     * 解析敏感词检测接口响应
     *
     * @param responseBodyStr 响应字符串
     * @return 敏感词检测结果
     */
    @Override
    public SensitiveWordsVO parseSensitiveCheckResponse(String responseBodyStr) {
        log.info("开始解析敏感词检测响应: {}", responseBodyStr);

        // 默认返回值：未命中敏感词
        SensitiveWordsVO defaultResult = SensitiveWordsVO.builder()
                .taskContentHitSensitiveWords(false)
                .machineRemarkHitSensitiveWords(false)
                .sensitiveWords(new HashSet<>())
                .build();

        try {
            if (StringUtil.isBlank(responseBodyStr)) {
                log.warn("响应字符串为空，返回默认结果");
                return defaultResult;
            }

            JSONObject responseJson = JSON.parseObject(responseBodyStr);

            // 检查result字段，如果是PASS则未命中敏感词
            String result = responseJson.getString("result");
            if ("PASS".equals(result)) {
                log.info("result字段为PASS，未命中敏感词");
                return defaultResult;
            }

            // 检查decision字段
            Object decisionObj = responseJson.get("decision");
            if (decisionObj == null) {
                log.info("decision字段为空，未命中敏感词");
                return defaultResult;
            }

            // 如果decision是字符串且为"accept"，则未命中敏感词
            if (decisionObj instanceof String && "accept".equals(decisionObj)) {
                log.info("decision字段为accept字符串，未命中敏感词");
                return defaultResult;
            }

            // 如果decision是对象，则需要解析其中的敏感词信息
            if (decisionObj instanceof JSONObject) {
                JSONObject decisionJson = (JSONObject) decisionObj;

                // 获取ruleDesc对象
                JSONObject ruleDescJson = decisionJson.getJSONObject("ruleDesc");
                if (ruleDescJson == null) {
                    log.info("ruleDesc字段为空，未命中敏感词");
                    return defaultResult;
                }

                boolean taskContentHit = false;
                boolean machineRemarkHit = false;
                HashSet<String> sensitiveWords = new HashSet<>();

                // 检查cargoName字段（对应货物名称）
                JSONObject cargoNameJson = ruleDescJson.getJSONObject("cargoName");
                if (cargoNameJson != null) {
                    taskContentHit = true;
                    extractSensitiveWords(cargoNameJson, sensitiveWords);
                    log.info("货物名称命中敏感词");
                }

                // 检查description字段（对应货名备注）
                JSONObject descriptionJson = ruleDescJson.getJSONObject("description");
                if (descriptionJson != null) {
                    machineRemarkHit = true;
                    extractSensitiveWords(descriptionJson, sensitiveWords);
                    log.info("货名备注命中敏感词");
                }

                SensitiveWordsVO result1 = SensitiveWordsVO.builder()
                        .taskContentHitSensitiveWords(taskContentHit)
                        .machineRemarkHitSensitiveWords(machineRemarkHit)
                        .sensitiveWords(sensitiveWords)
                        .build();

                log.info("解析完成，命中敏感词数量: {}", sensitiveWords.size());
                return result1;
            }

            log.info("decision字段格式不符合预期，返回默认结果");
            return defaultResult;

        } catch (Exception e) {
            log.error("解析敏感词检测响应异常", e);
            // 解析失败时返回默认结果，避免阻断业务流程
            return defaultResult;
        }
    }

    /**
     * 从敏感词检测结果中提取敏感词
     *
     * @param sensitiveJson 敏感词检测结果JSON对象
     * @param sensitiveWords 敏感词集合
     */
    private void extractSensitiveWords(JSONObject sensitiveJson, HashSet<String> sensitiveWords) {
        if (sensitiveJson == null) {
            return;
        }

        // 获取hitDetails数组
        Object hitDetailsObj = sensitiveJson.get("hitDetails");
        if (hitDetailsObj instanceof com.alibaba.fastjson.JSONArray) {
            com.alibaba.fastjson.JSONArray hitDetailsArray = (com.alibaba.fastjson.JSONArray) hitDetailsObj;

            for (int i = 0; i < hitDetailsArray.size(); i++) {
                JSONObject hitDetail = hitDetailsArray.getJSONObject(i);
                if (hitDetail != null) {
                    String hitText = hitDetail.getString("hitText");
                    if (StringUtil.isNotBlank(hitText)) {
                        sensitiveWords.add(hitText);
                        log.debug("提取到敏感词: {}", hitText);
                    }
                }
            }
        }
    }



}

