package com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.wlqq.wallet.gateway.client.util.sign.RSA.sign;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class SensitiveWordsServiceImplTest {

    @Mock
    private MBOpenPlatformUtil util;

    @InjectMocks
    private SensitiveWordsServiceImpl sensitiveWordsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testNewCargoSensitiveCheck_Accept() throws Exception {
        // 模拟成功返回（未命中敏感词）
        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";
        when(util.doPost(anyString(), any())).thenReturn(acceptResponse);

        SensitiveWordsVO result = sensitiveWordsService.newCargoSensitiveCheck(123L, "正常货物", "正常描述");

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testNewCargoSensitiveCheck_HitSensitive() throws Exception {
        // 模拟命中敏感词的返回
        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";
        when(util.doPost(anyString(), any())).thenReturn(sensitiveResponse);

        SensitiveWordsVO result = sensitiveWordsService.newCargoSensitiveCheck(123L, "含有火药和枪的货物", "傻逼描述");

        assertNotNull(result);
        assertTrue(result.getTaskContentHitSensitiveWords());
        assertTrue(result.getMachineRemarkHitSensitiveWords());
        assertEquals(3, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("枪"));
        assertTrue(result.getSensitiveWords().contains("火药"));
        assertTrue(result.getSensitiveWords().contains("傻逼"));
    }

    @Test
    void testParseSensitiveCheckResponse_Accept() throws Exception {
        // 使用反射调用私有方法进行测试
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, acceptResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testParseSensitiveCheckResponse_HitSensitive() throws Exception {
        // 使用反射调用私有方法进行测试
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, sensitiveResponse);

        assertNotNull(result);
        assertTrue(result.getTaskContentHitSensitiveWords());
        assertTrue(result.getMachineRemarkHitSensitiveWords());
        assertEquals(3, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("枪"));
        assertTrue(result.getSensitiveWords().contains("火药"));
        assertTrue(result.getSensitiveWords().contains("傻逼"));
    }

    @Test
    void testParseSensitiveCheckResponse_InvalidJson() throws Exception {
        // 测试无效JSON的情况
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String invalidResponse = "invalid json";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, invalidResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords()); // 解析失败时为了不阻断流程认为未命中敏感词
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testReal() throws Exception {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        // 内层 params
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("description", "zhao驾驶元");
        params.put("scene", "tyt");
        params.put("ticketType", "1");
        params.put("cargoName", "zhao驾驶元");
        params.put("securedTran", "2");
        params.put("filterType", "0");

        // 外层对象
        Map<String, Object> root = new LinkedHashMap<>();
        root.put("interfaceName", "NewCargoSensitiveCheck");
        root.put("params", params);

        // 转为带缩进的 JSON 字符串
        String content = JSON.toJSONString(root, SerializerFeature.PrettyFormat);

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, content);
        Request request = new Request.Builder()
                .url(
                        "https://dev.ymm56.com/risk-access-service/RiskAccessFacade/riskExternalCommonRuleIface")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("x-app-id", "risk-access-service")
                .addHeader("x-sign-type", "RSA")
                .addHeader("x-sign", sign(content,
                        "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCbVftVEN0GpI8SNbgeVZ0hu84LL1nTZQ3aWPZdGgtjldl+em/6gNzLsm3KRkukBCvLgIdbo9JP/Th2fUpKmiRSik5lMPS+vluzSMpYE0RrAyeKM1H68D6BtLH5sVu9P3Itxa2WTmWsk70GRVwpW2ReMlYcb+Z52YozEA4AXSlgWERScpL8eFzGqnikZ9rrHoqjd9cUo8+jiCd7Cyh7VHMT1KxcRW4wkj0Z8KxiyGW7MzGmBwlZ61fqub+JjtNYNtiUw09dRVIKB/BaeTOIvy0w/Nf2bnfuesV/apHvIryEf6IdSDqTa08lRVccVZ0xAeyg21DFvuiSpDzwycY/9oq3AgMBAAECggEBAJQRA3/0kRrx5UbQWe7gYOV05VSYECGHJAtL3ZqlZtHIVwdOokrAZrbsH+c91oBDUZOrslrR73k7wISBrT0rbRAYsd2XKtRfE330FpyZ0Q/Ic/oyLSTruEle+n8uZmogfi/PTEYH+LbmJLpDhzQM38FCbf6xU5o2mJtP5GjIZNZmECm/qUHrIkYFrBgg308gJVs1mNXyYJhghQm5xpildX5Bj9cqw0G3JEqTe21q5Plb3TdzLmlTYvCAEjwGvj2ZMV3vPka0BDgXeSMGJTNpWjBO1gTw8GIfqKp7zFITPGgHhIRL/GY+XTFPLeqnik2lkfq8RigusYYjKV9+ji37IPECgYEA0W64eMKoRkPYj8Sn2xDKNBMI1EjYxja0Axygul+uShhjz2vLZFIBwGMB3TgRaSJ4gtcw0evXDHs0Unyz8XlVSuLQsvNovuAuqKioyYt3RJsOQaJMAHKqEBT06+LXPDgruL+/D2qNUpa+nQeUhkDahNNRvRoMjrXvoGPSsoyPUYsCgYEAvd/7bZi6dUSF2+73RFRzexA2aNylrXJ+5gePYl6kdGsyQQAi/OB7CdM7/YcOm5rZOgsVXeQ1RiZTzgYr22cTQgR2PKC7cW/PfTQsY0uMv3GHlmtyuV3NNKxtPpddh746G8ma96F//Vx9E0L/eR7a2UptDz0pr13tkSwIOWRGOQUCgYA8W/S+mRt2ZMddaAgYktYz++hnhjczqUBoRS/thvXsVOsvNJoB58Cped9nqmkaK90fmHVxis8Hwdpqi5Yw2g9C7evIJ27g1CueYD2FGLTfCPEq6WOOHX319JRCB7aQnH+9V0XxY3wneVqmVzDvIHHaJi1AmulTmAvf5eUroGcN0QKBgQClJ+c+DEPbbs/0XuaLc93C5HY/PfWKafYBRgAtM19euENNEL5eMTA0ep1KyCgnp0hd2XgrZC4Gp53rraiGPiEdIl5qG/EPGY9kO3aEpBl4CFtEto9itzmgIH8hm23KfedzdigqTzrR+Zn9oCAxjWoU6Uz1nWDd3hifVqqMqTD/tQKBgQCpTe794fJwu2CXrNT7ougavlXnG2ex57ybCBvobP8/JrmZY/tS3HNpy7Q/7QCoqDlGGlC6ZhjHYld6oDt2reYgffR1CzBtobsKjaBvkYlZqst++hfRlpr2aDEXOjijRywlCZBLDlonVqWnQ7LEDTwx5FITsal5wQBZ1GD8KJXiTQ==",
                        "UTF-8"))
                .build();
        Response response = client.newCall(request).execute();
        if (response.isSuccessful() && response.body() != null) {
            SensitiveWordsVO sensitiveWordsVO = sensitiveWordsService.parseSensitiveCheckResponse(response.body().string());
            System.out.println(1);
        }

    }

    @Test
    void testParseSensitiveCheckResponse_YourExample() {
        // 测试你提供的JSON示例
        String yourExampleResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":0,\"dict\":\"CARGO_EXTRA_DICT\",\"endIndex\":7,\"hitText\":\"zhao驾驶元\",\"index\":0,\"tag\":\"cargoExtra\"}]}}},\"rules\":[\"NewCargoSensitiveCheck_sensitiveRule\"],\"extend\":\"货源敏感词\",\"result\":\"REJECT\",\"riskButtonModel\":null}";

        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse(yourExampleResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords()); // cargoName未命中
        assertTrue(result.getMachineRemarkHitSensitiveWords()); // description命中
        assertEquals(1, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("zhao驾驶元"));
    }

    @Test
    void testParseSensitiveCheckResponse_AcceptString() {
        // 测试decision为"accept"字符串的情况
        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";

        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse(acceptResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testParseSensitiveCheckResponse_PassResult() {
        // 测试result为"PASS"的情况
        String passResponse = "{\"decision\":{\"ruleCode\":\"3113\"},\"result\":\"PASS\"}";

        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse(passResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testParseSensitiveCheckResponse_BothCargoNameAndDescription() {
        // 测试cargoName和description都命中敏感词的情况
        String bothHitResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":0,\"endIndex\":2,\"hitText\":\"枪支\",\"index\":1}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":0,\"endIndex\":2,\"hitText\":\"毒品\",\"index\":2}]}}},\"rules\":[\"NewCargoSensitiveCheck_sensitiveRule\"],\"result\":\"REJECT\"}";

        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse(bothHitResponse);

        assertNotNull(result);
        assertTrue(result.getTaskContentHitSensitiveWords()); // cargoName命中
        assertTrue(result.getMachineRemarkHitSensitiveWords()); // description命中
        assertEquals(2, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("枪支"));
        assertTrue(result.getSensitiveWords().contains("毒品"));
    }

    @Test
    void testParseSensitiveCheckResponse_EmptyResponse() {
        // 测试空响应
        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse("");

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testParseSensitiveCheckResponse_NullResponse() {
        // 测试null响应
        SensitiveWordsVO result = sensitiveWordsService.parseSensitiveCheckResponse(null);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }
}
